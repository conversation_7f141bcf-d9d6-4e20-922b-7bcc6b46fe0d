# Appwrite App - Flutter Firebase Authentication

Ứng dụng Flutter với giao diện đăng nhập đẹp mắt và tích hợp Firebase Authentication.

## Tính năng

✅ **Giao diện đăng nhập đẹp mắt** - Thiết kế theo mockup với màu hồng chủ đạo
✅ **Đăng ký tài khoản** - Tạo tài khoản mới với email và password
✅ **Đăng nhập cơ bản** - Đăng nhập với email và password
✅ **Đăng nhập Google** - Tích hợp Google Sign-In
✅ **Xem như khách** - Cho phép xem ứng dụng mà không cần đăng nhập
✅ **Quản lý trạng thái** - Tự động điều hướng dựa trên trạng thái đăng nhập
✅ **Validation** - Kiể<PERSON> tra dữ liệu đầu vào
✅ **Xử lý lỗi** - Hiển thị thông báo lỗi bằng tiếng Việt

## Cấu trúc dự án

```
lib/
├── main.dart                    # Entry point và AuthWrapper
├── firebase_options.dart        # Cấu hình Firebase
├── services/
│   └── auth_service.dart        # Service xử lý authentication
├── screens/
│   ├── login_screen.dart        # Màn hình đăng nhập
│   ├── register_screen.dart     # Màn hình đăng ký
│   └── home_screen.dart         # Màn hình chính
└── widgets/
    ├── custom_text_field.dart   # Widget input field tùy chỉnh
    └── social_login_button.dart # Widget button đăng nhập mạng xã hội
```

## Cài đặt và chạy

1. **Clone dự án**
```bash
git clone <repository-url>
cd bt01
```

2. **Cài đặt dependencies**
```bash
flutter pub get
```

3. **Cấu hình Firebase**
   - Tạo project Firebase tại [Firebase Console](https://console.firebase.google.com/)
   - Thêm ứng dụng Android/iOS
   - Tải file `google-services.json` (Android) và `GoogleService-Info.plist` (iOS)
   - Đặt file vào thư mục tương ứng
   - Chạy `flutterfire configure` để tạo `firebase_options.dart`

4. **Chạy ứng dụng**
```bash
flutter run
```

## Cấu hình Firebase Authentication

1. Trong Firebase Console, vào **Authentication** > **Sign-in method**
2. Bật các phương thức đăng nhập:
   - **Email/Password**
   - **Google** (cần cấu hình OAuth consent screen)

## Sử dụng

### Đăng nhập
- Nhập email và password đã đăng ký
- Hoặc nhấn nút Google để đăng nhập bằng tài khoản Google
- Hoặc nhấn "Read Messages as Guest" để xem như khách

### Đăng ký
- Nhấn "Create Account" từ màn hình đăng nhập
- Nhập email, password và xác nhận password
- Nhấn "Đăng Ký"

### Đăng xuất
- Từ màn hình chính, nhấn icon logout ở góc phải header
- Xác nhận đăng xuất

## Dependencies chính

- `firebase_core` - Core Firebase SDK
- `firebase_auth` - Firebase Authentication
- `google_sign_in` - Google Sign-In
- `font_awesome_flutter` - Icons cho social login

## Ghi chú

- Ứng dụng hỗ trợ tiếng Việt cho các thông báo lỗi
- Giao diện responsive và thân thiện với người dùng
- Xử lý trạng thái loading và error một cách mượt mà
- Code được tổ chức theo pattern clean architecture

## Phát triển tiếp

- [ ] Thêm Apple Sign-In
- [ ] Thêm GitHub Sign-In
- [ ] Thêm Twitter Sign-In
- [ ] Thêm chức năng reset password
- [ ] Thêm chức năng tin nhắn thực tế
- [ ] Thêm profile management
