import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class SocialLoginButton extends StatelessWidget {
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;
  final String tooltip;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.color,
    required this.onPressed,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: 50,
        height: 50,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(25),
            onTap: onPressed,
            child: Center(
              child: FaIcon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SocialLoginRow extends StatelessWidget {
  final VoidCallback onGooglePressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onGithubPressed;
  final VoidCallback? onTwitterPressed;

  const SocialLoginRow({
    super.key,
    required this.onGooglePressed,
    this.onApplePressed,
    this.onGithubPressed,
    this.onTwitterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SocialLoginButton(
          icon: FontAwesomeIcons.google,
          color: const Color(0xFFDB4437),
          onPressed: onGooglePressed,
          tooltip: 'Đăng nhập với Google',
        ),
        if (onApplePressed != null)
          SocialLoginButton(
            icon: FontAwesomeIcons.apple,
            color: Colors.black,
            onPressed: onApplePressed!,
            tooltip: 'Đăng nhập với Apple',
          ),
        if (onGithubPressed != null)
          SocialLoginButton(
            icon: FontAwesomeIcons.github,
            color: const Color(0xFF333333),
            onPressed: onGithubPressed!,
            tooltip: 'Đăng nhập với GitHub',
          ),
        if (onTwitterPressed != null)
          SocialLoginButton(
            icon: FontAwesomeIcons.twitter,
            color: const Color(0xFF1DA1F2),
            onPressed: onTwitterPressed!,
            tooltip: 'Đăng nhập với Twitter',
          ),
      ],
    );
  }
}
