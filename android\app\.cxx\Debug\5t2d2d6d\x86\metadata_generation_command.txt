                        -HD:\File\FileFlutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\File\FileAndroidSDK\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\File\FileAndroidSDK\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\File\FileAndroidSDK\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\File\FileAndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Firebase\bt01\build\app\intermediates\cxx\Debug\5t2d2d6d\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Firebase\bt01\build\app\intermediates\cxx\Debug\5t2d2d6d\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\Firebase\bt01\android\app\.cxx\Debug\5t2d2d6d\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2