{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/File/FileAndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/File/FileAndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-727dfe1dd15673a61f15.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e67ded38105c60e723c7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c3692cd6f140f0cbea68.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e67ded38105c60e723c7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c3692cd6f140f0cbea68.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-727dfe1dd15673a61f15.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}